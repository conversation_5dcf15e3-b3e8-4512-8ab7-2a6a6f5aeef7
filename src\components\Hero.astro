<!-- ====== Hero Section Start -->
<section
  id="home"
  class="relative overflow-hidden bg-primary pt-[120px] md:pt-[130px] lg:pt-[160px]"
>
  <div class="container">
    <div class="-mx-4 flex flex-wrap items-center">
      <div class="w-full px-4">
        <div
          class="hero-content wow fadeInUp mx-auto max-w-[780px] text-center"
          data-wow-delay=".2s"
        >
          <h1
            class="mb-6 text-3xl font-bold leading-snug text-white sm:text-4xl sm:leading-snug lg:text-5xl lg:leading-[1.2]"
          >
            AI-Powered Amazon Ad Assistant
            </h1>
          <p
            class="mx-auto mb-9 max-w-[600px] font-medium text-base text-white sm:text-lg sm:leading-[1.44]"
          >
One-click AI Automation, Lower ACOS, Better Sales. Intelligent Amazon advertising optimization that helps sellers save time, reduce costs, and increase revenue with advanced machine learning algorithms.
          </p>
          <ul class="mb-10 flex gap-5 flex-wrap items-center justify-center">
            <li>
              <a
                href="javascript:void(0)"
                class="inline-flex items-center justify-center rounded-md bg-white py-[14px] px-7 text-center text-base font-medium text-dark shadow-1 transition duration-300 ease-in-out hover:text-body-color hover:bg-gray-2"
              >
                Try for Free
              </a>
            </li>
            <li>
              <a
                href="javascript:void(0)"
                class="flex items-center gap-4 py-[14px] rounded-md bg-white/[0.12] px-6 text-base font-medium text-white transition duration-300 ease-in-out hover:bg-white hover:text-dark"
              >
                <svg class="fill-current" width="24" height="24" viewBox="0 0 24 24" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_2005_10818)">
                    <path
                      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                  </g>
                  <defs>
                    <clipPath id="clip0_2005_10818">
                      <rect width="24" height="24" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
                Book a Demo
              </a>
            </li>
          </ul>
          <div class="wow fadeInUp text-center flex items-center justify-center gap-4" data-wow-delay=".3s">
            <a href="https://github.com/uideck/play-bootstrap/" class="text-white/60 hover:text-white duration-300 ease-in-out" target="_blank">
              <svg class="fill-current" width="41" height="32" viewBox="0 0 41 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <mask id="mask0_2005_10788" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="41" height="32">
                <path d="M0.521393 0.0454102H40.5214V31.9174H0.521393V0.0454102Z"/>
                </mask>
                <g mask="url(#mask0_2005_10788)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M8.82951 0.048584C6.54719 0.048584 4.85835 2.04626 4.93395 4.21266C5.00655 6.29398 4.91223 8.98962 4.23366 11.1879C3.55264 13.3923 2.4017 14.7893 0.521393 14.9686V16.993C2.4017 17.1727 3.55264 18.5689 4.23358 20.7737C4.91223 22.9719 5.00647 25.6676 4.93387 27.7489C4.85827 29.915 6.54711 31.913 8.82983 31.913H32.2163C34.4987 31.913 36.1872 29.9153 36.1116 27.7489C36.039 25.6676 36.1333 22.9719 36.8119 20.7737C37.4929 18.5689 38.641 17.1721 40.5214 16.993V14.9686C38.6411 14.7889 37.493 13.3927 36.8119 11.1879C36.1332 8.9899 36.039 6.29398 36.1116 4.21266C36.1872 2.04654 34.4987 0.048584 32.2163 0.048584H8.82951ZM27.6401 19.6632C27.6401 22.6463 25.415 24.4554 21.7224 24.4554H15.4366C15.2568 24.4554 15.0844 24.3839 14.9572 24.2568C14.8301 24.1297 14.7587 23.9572 14.7587 23.7774V8.18422C14.7587 8.00442 14.8301 7.83194 14.9572 7.70482C15.0844 7.57766 15.2568 7.50626 15.4366 7.50626H21.6866C24.7656 7.50626 26.7863 9.17406 26.7863 11.7347C26.7863 13.5319 25.427 15.1409 23.6952 15.4228V15.5165C26.0526 15.7751 27.6401 17.408 27.6401 19.6632ZM21.037 9.65538H17.453V14.7179H20.4716C22.8052 14.7179 24.092 13.7782 24.092 12.0986C24.0917 10.5245 22.9855 9.65538 21.037 9.65538ZM17.453 16.7265V22.3055H21.1689C23.5986 22.3055 24.8856 21.3306 24.8856 19.4984C24.8856 17.6663 23.5625 16.7263 21.0126 16.7263L17.453 16.7265Z"/>
                </g>
              </svg>
            </a>

            <a href="https://github.com/TailGrids/play-tailwind/" class="text-white/60 hover:text-white duration-300 ease-in-out" target="_blank">
              <svg class="fill-current" width="41" height="26" viewBox="0 0 41 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                <mask id="mask0_2005_10783" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="41" height="26">
                <path d="M0.521393 0.949463H40.5214V25.0135H0.521393V0.949463Z" />
                </mask>
                <g mask="url(#mask0_2005_10783)">
                <path d="M20.5214 0.980713C15.1882 0.980713 11.8546 3.64743 10.5214 8.98071C12.5214 6.31399 14.8546 5.31399 17.5214 5.98071C19.043 6.36103 20.1302 7.46495 21.3342 8.68667C23.295 10.6771 25.5642 12.9807 30.5214 12.9807C35.8546 12.9807 39.1882 10.314 40.5214 4.98071C38.5214 7.64743 36.1882 8.64743 33.5214 7.98071C31.9998 7.60039 30.9126 6.49651 29.7086 5.27479C27.7478 3.28431 25.4786 0.980713 20.5214 0.980713ZM10.5214 12.9807C5.18819 12.9807 1.85459 15.6474 0.521393 20.9807C2.52139 18.314 4.85459 17.314 7.52139 17.9807C9.04299 18.361 10.1302 19.465 11.3342 20.6867C13.295 22.6771 15.5642 24.9807 20.5214 24.9807C25.8546 24.9807 29.1882 22.314 30.5214 16.9807C28.5214 19.6474 26.1882 20.6474 23.5214 19.9807C21.9998 19.6004 20.9126 18.4965 19.7086 17.2748C17.7478 15.2843 15.4786 12.9807 10.5214 12.9807Z"/>
                </g>
              </svg>
            </a>

            <a href="https://github.com/NextJSTemplates/play-nextjs" class="text-white/60 hover:text-white duration-300 ease-in-out" target="_blank">
              <svg class="fill-current" width="41" height="36" viewBox="0 0 41 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M40.5214 17.9856C40.5214 15.3358 37.203 12.8245 32.1154 11.2673C33.2894 6.08177 32.7678 1.95622 30.4686 0.63539C29.9386 0.325566 29.3186 0.178806 28.6422 0.178806V1.99699C29.017 1.99699 29.3186 2.07037 29.5714 2.20897C30.6802 2.84493 31.1614 5.26645 30.7862 8.38101C30.6966 9.14741 30.5498 9.95457 30.3706 10.7781C28.7726 10.3867 27.0278 10.0851 25.1934 9.88937C24.0926 8.38101 22.951 7.01125 21.8014 5.81273C24.4594 3.34229 26.9542 1.98883 28.6502 1.98883V0.170654C26.4082 0.170654 23.473 1.7687 20.505 4.54081C17.5374 1.78501 14.6022 0.203266 12.3598 0.203266V2.02145C14.0478 2.02145 16.5506 3.36673 19.2086 5.82089C18.0674 7.01941 16.9258 8.38101 15.8414 9.88937C13.9986 10.0851 12.2538 10.3867 10.6558 10.7862C10.4686 9.97089 10.3298 9.18001 10.2318 8.42177C9.84859 5.30721 10.3218 2.88569 11.4222 2.24157C11.667 2.09483 11.985 2.0296 12.3598 2.0296V0.211422C11.675 0.211422 11.0554 0.358178 10.5174 0.668006C8.22619 1.98883 7.71259 6.10626 8.89499 11.2754C3.82339 12.8409 0.521393 15.3439 0.521393 17.9856C0.521393 20.6354 3.8398 23.1466 8.9274 24.7039C7.7534 29.8894 8.27499 34.0149 10.5742 35.3358C11.1042 35.6456 11.7242 35.7923 12.409 35.7923C14.651 35.7923 17.5862 34.1943 20.5542 31.4222C23.5218 34.178 26.457 35.7597 28.699 35.7597C29.3842 35.7597 30.0038 35.613 30.5418 35.3031C32.833 33.9823 33.3466 29.8649 32.1642 24.6957C37.2194 23.1385 40.5214 20.6273 40.5214 17.9856ZM29.9058 12.5473C29.6042 13.5991 29.229 14.6835 28.805 15.7679C28.471 15.1156 28.1202 14.4634 27.737 13.8111C27.3622 13.1588 26.9626 12.5229 26.563 11.9032C27.7206 12.0745 28.8378 12.2864 29.9058 12.5473ZM26.1718 21.2306C25.5358 22.3313 24.8834 23.3749 24.2066 24.3451C22.9918 24.4511 21.7606 24.5082 20.5214 24.5082C19.2902 24.5082 18.059 24.4511 16.8526 24.3533C16.1758 23.3831 15.5154 22.3476 14.8794 21.2551C14.2598 20.187 13.697 19.1026 13.1834 18.01C13.689 16.9175 14.2598 15.8249 14.871 14.7569C15.507 13.6562 16.1594 12.6126 16.8362 11.6423C18.051 11.5363 19.2822 11.4793 20.5214 11.4793C21.7526 11.4793 22.9838 11.5363 24.1902 11.6342C24.867 12.6044 25.5274 13.6399 26.1634 14.7324C26.783 15.8005 27.3458 16.8849 27.8594 17.9774C27.3458 19.07 26.783 20.1625 26.1718 21.2306ZM28.805 20.1707C29.2454 21.2632 29.6206 22.3557 29.9302 23.4157C28.8622 23.6766 27.737 23.8967 26.571 24.0679C26.9706 23.4401 27.3702 22.796 27.7454 22.1356C28.1202 21.4833 28.471 20.8229 28.805 20.1707ZM20.5378 28.8702C19.7794 28.0875 19.021 27.2151 18.271 26.2611C19.005 26.2938 19.755 26.3182 20.5134 26.3182C21.2798 26.3182 22.0378 26.3019 22.7798 26.2611C22.0462 27.2151 21.2878 28.0875 20.5378 28.8702ZM14.4718 24.0679C13.3138 23.8967 12.197 23.6847 11.129 23.4238C11.4306 22.3721 11.8054 21.2877 12.2294 20.2033C12.5638 20.8555 12.9142 21.5078 13.2974 22.1601C13.6806 22.8123 14.0722 23.4483 14.4718 24.0679ZM20.497 7.10093C21.255 7.88365 22.0134 8.75605 22.7634 9.70998C22.0298 9.67737 21.2798 9.65293 20.5214 9.65293C19.755 9.65293 18.9966 9.66922 18.2546 9.70998C18.9886 8.75605 19.747 7.88365 20.497 7.10093ZM14.4634 11.9032C14.0642 12.531 13.6646 13.1751 13.2894 13.8356C12.9142 14.4878 12.5638 15.1401 12.2294 15.7923C11.7894 14.6998 11.4142 13.6073 11.1042 12.5473C12.1726 12.2946 13.2974 12.0745 14.4634 11.9032ZM7.08459 22.1111C4.19859 20.88 2.33139 19.2657 2.33139 17.9856C2.33139 16.7055 4.19859 15.083 7.08459 13.86C7.78579 13.5583 8.55219 13.2893 9.34339 13.0365C9.80779 14.6346 10.4194 16.2979 11.1778 18.0019C10.4278 19.6978 9.82419 21.3529 9.36779 22.9428C8.56059 22.69 7.79419 22.4128 7.08459 22.1111ZM11.4714 33.7622C10.3626 33.1262 9.8814 30.7047 10.2566 27.5901C10.3462 26.8237 10.493 26.0166 10.6722 25.1931C12.2702 25.5844 14.015 25.8861 15.8494 26.0818C16.9502 27.5901 18.0918 28.9599 19.2414 30.1584C16.5834 32.6289 14.0886 33.9823 12.3926 33.9823C12.0258 33.9742 11.7158 33.9008 11.4714 33.7622ZM30.811 27.5494C31.1942 30.6639 30.721 33.0855 29.6206 33.7296C29.3758 33.8763 29.0578 33.9415 28.683 33.9415C26.995 33.9415 24.4922 32.5963 21.8342 30.1421C22.9754 28.9436 24.117 27.582 25.2014 26.0736C27.0442 25.8779 28.789 25.5763 30.387 25.1768C30.5742 26.0003 30.721 26.7911 30.811 27.5494ZM33.9498 22.1111C33.2486 22.4128 32.4822 22.6819 31.6914 22.9346C31.2266 21.3366 30.615 19.6733 29.857 17.9693C30.607 16.2734 31.2102 14.6183 31.667 13.0284C32.4742 13.2811 33.2406 13.5583 33.9582 13.86C36.8442 15.0912 38.7114 16.7055 38.7114 17.9856C38.7034 19.2657 36.8362 20.8881 33.9498 22.1111Z" />
                <path d="M20.5134 21.7133C22.5714 21.7133 24.2394 20.0451 24.2394 17.9873C24.2394 15.9294 22.5714 14.2612 20.5134 14.2612C18.4558 14.2612 16.7874 15.9294 16.7874 17.9873C16.7874 20.0451 18.4558 21.7133 20.5134 21.7133Z"/>
              </svg>
            </a>

            <a href="https://github.com/TailGrids/play-astro/" class="text-white/60 hover:text-white duration-300 ease-in-out" target="_blank">
              <svg class="fill-current mt-0.5" width="30" height="38" viewBox="0 0 30 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_2412_2096)">
                <path d="M9.54022 32.0145C7.86872 30.4866 7.38074 27.2761 8.07717 24.9502C9.28468 26.4166 10.9578 26.8812 12.6908 27.1434C15.3662 27.548 17.9937 27.3967 20.479 26.1739C20.7633 26.0338 21.0261 25.8477 21.3368 25.6591C21.57 26.3357 21.6306 27.0187 21.5492 27.7139C21.3511 29.407 20.5086 30.7148 19.1685 31.7062C18.6326 32.1027 18.0656 32.4572 17.5121 32.8311C15.8119 33.9803 15.3519 35.3278 15.9907 37.2877C16.0059 37.3358 16.0195 37.3835 16.0538 37.5C15.1857 37.1114 14.5516 36.5456 14.0684 35.8018C13.5581 35.017 13.3153 34.1486 13.3026 33.209C13.2962 32.7518 13.2962 32.2905 13.2347 31.8397C13.0845 30.7407 12.5686 30.2486 11.5967 30.2203C10.5992 30.1912 9.81018 30.8078 9.60094 31.779C9.58497 31.8535 9.5618 31.9271 9.53863 32.0137L9.54022 32.0145Z" />
                <path d="M0 24.5627C0 24.5627 4.94967 22.1515 9.91317 22.1515L13.6555 10.5697C13.7956 10.0096 14.2046 9.62894 14.6665 9.62894C15.1283 9.62894 15.5374 10.0096 15.6775 10.5697L19.4198 22.1515C25.2984 22.1515 29.333 24.5627 29.333 24.5627C29.333 24.5627 20.9256 1.65922 20.9092 1.61326C20.6679 0.936116 20.2605 0.5 19.7113 0.5H9.62256C9.07337 0.5 8.68245 0.936116 8.42473 1.61326C8.40654 1.65835 0 24.5627 0 24.5627Z" />
                </g>
                <defs>
                <clipPath id="clip0_2412_2096">
                <rect width="29.3925" height="37" transform="translate(0 0.5)"/>
                </clipPath>
                </defs>
              </svg>
            </a>

            <a href="https://github.com/NextJSTemplates/play-nextjs" class="text-white/60 hover:text-white duration-300 ease-in-out" target="_blank">
              <svg class="fill-current" width="41" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19.1914 0.0107542C19.1054 0.0185659 18.8322 0.0459068 18.5862 0.0654364C12.911 0.577104 7.59499 3.63931 4.22819 8.34588C2.35339 10.9628 1.15419 13.9313 0.700995 17.0755C0.540995 18.173 0.521393 18.4972 0.521393 19.9854C0.521393 21.4735 0.540995 21.7977 0.700995 22.8952C1.78699 30.3984 7.12619 36.7025 14.3678 39.0382C15.6646 39.4561 17.0314 39.7412 18.5862 39.9131C19.1914 39.9795 21.8082 39.9795 22.4138 39.9131C25.097 39.6163 27.3702 38.9523 29.6122 37.8078C29.9562 37.6321 30.0226 37.5852 29.9754 37.5462C29.9442 37.5227 28.4798 35.5581 26.7218 33.1833L23.527 28.8673L19.5234 22.9421C17.3206 19.6846 15.5082 17.0208 15.4926 17.0208C15.477 17.0169 15.4614 19.6495 15.4534 22.864C15.4418 28.4924 15.4378 28.7189 15.3678 28.8517C15.2662 29.0431 15.1878 29.1212 15.0238 29.2071C14.899 29.2696 14.7894 29.2813 14.1998 29.2813H13.5242L13.3442 29.1681C13.227 29.0938 13.1414 28.9962 13.0826 28.8829L13.0006 28.7072L13.0086 20.8759L13.0202 13.0407L13.1414 12.8884C13.2038 12.8064 13.3366 12.7009 13.4302 12.6502C13.5906 12.572 13.653 12.5642 14.3286 12.5642C15.1254 12.5642 15.2582 12.5955 15.4654 12.822C15.5238 12.8845 17.6914 16.1498 20.285 20.083C22.8786 24.0162 26.425 29.3868 28.167 32.0232L31.331 36.8158L31.491 36.7103C32.909 35.7885 34.4086 34.4761 35.5962 33.1091C38.123 30.207 39.7518 26.6683 40.2986 22.8952C40.459 21.7977 40.4786 21.4735 40.4786 19.9854C40.4786 18.4972 40.459 18.173 40.2986 17.0755C39.213 9.57232 33.8738 3.26825 26.6322 0.93254C25.355 0.518516 23.9958 0.233389 22.4722 0.0615304C22.0974 0.0224718 19.5158 -0.0204928 19.1914 0.0107542ZM27.3702 12.0955C27.5578 12.1892 27.7102 12.3689 27.765 12.5564C27.7962 12.658 27.8038 14.8296 27.7962 19.7237L27.7842 26.7464L26.5462 24.8482L25.3042 22.9499V17.845C25.3042 14.5445 25.3198 12.6892 25.343 12.5994C25.4058 12.3806 25.5422 12.2088 25.7298 12.1072C25.8902 12.0252 25.9486 12.0174 26.5618 12.0174C27.1398 12.0174 27.2414 12.0252 27.3702 12.0955Z"/>
              </svg>
            </a>
          </div>
        </div>
      </div>

      <div class="w-full px-4">
        <div class="wow fadeInUp relative z-10 mx-auto max-w-[845px]" data-wow-delay=".25s">
          <div class="mt-16">
            <img
              src="/assets/hero/hero-image.jpg"
              alt="hero"
              class="mx-auto max-w-full rounded-t-xl rounded-tr-xl"
            />
          </div>
          <div class="absolute -left-9 bottom-0 z-[-1]">
            <svg
              width="134"
              height="106"
              viewBox="0 0 134 106"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle
                cx="1.66667"
                cy="104"
                r="1.66667"
                transform="rotate(-90 1.66667 104)"
                fill="white"></circle>
              <circle
                cx="16.3333"
                cy="104"
                r="1.66667"
                transform="rotate(-90 16.3333 104)"
                fill="white"></circle>
              <circle cx="31" cy="104" r="1.66667" transform="rotate(-90 31 104)" fill="white"
              ></circle>
              <circle
                cx="45.6667"
                cy="104"
                r="1.66667"
                transform="rotate(-90 45.6667 104)"
                fill="white"></circle>
              <circle
                cx="60.3333"
                cy="104"
                r="1.66667"
                transform="rotate(-90 60.3333 104)"
                fill="white"></circle>
              <circle
                cx="88.6667"
                cy="104"
                r="1.66667"
                transform="rotate(-90 88.6667 104)"
                fill="white"></circle>
              <circle
                cx="117.667"
                cy="104"
                r="1.66667"
                transform="rotate(-90 117.667 104)"
                fill="white"></circle>
              <circle
                cx="74.6667"
                cy="104"
                r="1.66667"
                transform="rotate(-90 74.6667 104)"
                fill="white"></circle>
              <circle cx="103" cy="104" r="1.66667" transform="rotate(-90 103 104)" fill="white"
              ></circle>
              <circle cx="132" cy="104" r="1.66667" transform="rotate(-90 132 104)" fill="white"
              ></circle>
              <circle
                cx="1.66667"
                cy="89.3333"
                r="1.66667"
                transform="rotate(-90 1.66667 89.3333)"
                fill="white"></circle>
              <circle
                cx="16.3333"
                cy="89.3333"
                r="1.66667"
                transform="rotate(-90 16.3333 89.3333)"
                fill="white"></circle>
              <circle
                cx="31"
                cy="89.3333"
                r="1.66667"
                transform="rotate(-90 31 89.3333)"
                fill="white"></circle>
              <circle
                cx="45.6667"
                cy="89.3333"
                r="1.66667"
                transform="rotate(-90 45.6667 89.3333)"
                fill="white"></circle>
              <circle
                cx="60.3333"
                cy="89.3338"
                r="1.66667"
                transform="rotate(-90 60.3333 89.3338)"
                fill="white"></circle>
              <circle
                cx="88.6667"
                cy="89.3338"
                r="1.66667"
                transform="rotate(-90 88.6667 89.3338)"
                fill="white"></circle>
              <circle
                cx="117.667"
                cy="89.3338"
                r="1.66667"
                transform="rotate(-90 117.667 89.3338)"
                fill="white"></circle>
              <circle
                cx="74.6667"
                cy="89.3338"
                r="1.66667"
                transform="rotate(-90 74.6667 89.3338)"
                fill="white"></circle>
              <circle
                cx="103"
                cy="89.3338"
                r="1.66667"
                transform="rotate(-90 103 89.3338)"
                fill="white"></circle>
              <circle
                cx="132"
                cy="89.3338"
                r="1.66667"
                transform="rotate(-90 132 89.3338)"
                fill="white"></circle>
              <circle
                cx="1.66667"
                cy="74.6673"
                r="1.66667"
                transform="rotate(-90 1.66667 74.6673)"
                fill="white"></circle>
              <circle
                cx="1.66667"
                cy="31.0003"
                r="1.66667"
                transform="rotate(-90 1.66667 31.0003)"
                fill="white"></circle>
              <circle
                cx="16.3333"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 16.3333 74.6668)"
                fill="white"></circle>
              <circle
                cx="16.3333"
                cy="31.0003"
                r="1.66667"
                transform="rotate(-90 16.3333 31.0003)"
                fill="white"></circle>
              <circle
                cx="31"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 31 74.6668)"
                fill="white"></circle>
              <circle
                cx="31"
                cy="31.0003"
                r="1.66667"
                transform="rotate(-90 31 31.0003)"
                fill="white"></circle>
              <circle
                cx="45.6667"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 45.6667 74.6668)"
                fill="white"></circle>
              <circle
                cx="45.6667"
                cy="31.0003"
                r="1.66667"
                transform="rotate(-90 45.6667 31.0003)"
                fill="white"></circle>
              <circle
                cx="60.3333"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 60.3333 74.6668)"
                fill="white"></circle>
              <circle
                cx="60.3333"
                cy="31.0001"
                r="1.66667"
                transform="rotate(-90 60.3333 31.0001)"
                fill="white"></circle>
              <circle
                cx="88.6667"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 88.6667 74.6668)"
                fill="white"></circle>
              <circle
                cx="88.6667"
                cy="31.0001"
                r="1.66667"
                transform="rotate(-90 88.6667 31.0001)"
                fill="white"></circle>
              <circle
                cx="117.667"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 117.667 74.6668)"
                fill="white"></circle>
              <circle
                cx="117.667"
                cy="31.0001"
                r="1.66667"
                transform="rotate(-90 117.667 31.0001)"
                fill="white"></circle>
              <circle
                cx="74.6667"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 74.6667 74.6668)"
                fill="white"></circle>
              <circle
                cx="74.6667"
                cy="31.0001"
                r="1.66667"
                transform="rotate(-90 74.6667 31.0001)"
                fill="white"></circle>
              <circle
                cx="103"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 103 74.6668)"
                fill="white"></circle>
              <circle
                cx="103"
                cy="31.0001"
                r="1.66667"
                transform="rotate(-90 103 31.0001)"
                fill="white"></circle>
              <circle
                cx="132"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 132 74.6668)"
                fill="white"></circle>
              <circle
                cx="132"
                cy="31.0001"
                r="1.66667"
                transform="rotate(-90 132 31.0001)"
                fill="white"></circle>
              <circle
                cx="1.66667"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 1.66667 60.0003)"
                fill="white"></circle>
              <circle
                cx="1.66667"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 1.66667 16.3336)"
                fill="white"></circle>
              <circle
                cx="16.3333"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 16.3333 60.0003)"
                fill="white"></circle>
              <circle
                cx="16.3333"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 16.3333 16.3336)"
                fill="white"></circle>
              <circle
                cx="31"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 31 60.0003)"
                fill="white"></circle>
              <circle
                cx="31"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 31 16.3336)"
                fill="white"></circle>
              <circle
                cx="45.6667"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 45.6667 60.0003)"
                fill="white"></circle>
              <circle
                cx="45.6667"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 45.6667 16.3336)"
                fill="white"></circle>
              <circle
                cx="60.3333"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 60.3333 60.0003)"
                fill="white"></circle>
              <circle
                cx="60.3333"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 60.3333 16.3336)"
                fill="white"></circle>
              <circle
                cx="88.6667"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 88.6667 60.0003)"
                fill="white"></circle>
              <circle
                cx="88.6667"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 88.6667 16.3336)"
                fill="white"></circle>
              <circle
                cx="117.667"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 117.667 60.0003)"
                fill="white"></circle>
              <circle
                cx="117.667"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 117.667 16.3336)"
                fill="white"></circle>
              <circle
                cx="74.6667"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 74.6667 60.0003)"
                fill="white"></circle>
              <circle
                cx="74.6667"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 74.6667 16.3336)"
                fill="white"></circle>
              <circle
                cx="103"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 103 60.0003)"
                fill="white"></circle>
              <circle
                cx="103"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 103 16.3336)"
                fill="white"></circle>
              <circle
                cx="132"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 132 60.0003)"
                fill="white"></circle>
              <circle
                cx="132"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 132 16.3336)"
                fill="white"></circle>
              <circle
                cx="1.66667"
                cy="45.3336"
                r="1.66667"
                transform="rotate(-90 1.66667 45.3336)"
                fill="white"></circle>
              <circle
                cx="1.66667"
                cy="1.66683"
                r="1.66667"
                transform="rotate(-90 1.66667 1.66683)"
                fill="white"></circle>
              <circle
                cx="16.3333"
                cy="45.3336"
                r="1.66667"
                transform="rotate(-90 16.3333 45.3336)"
                fill="white"></circle>
              <circle
                cx="16.3333"
                cy="1.66683"
                r="1.66667"
                transform="rotate(-90 16.3333 1.66683)"
                fill="white"></circle>
              <circle
                cx="31"
                cy="45.3336"
                r="1.66667"
                transform="rotate(-90 31 45.3336)"
                fill="white"></circle>
              <circle
                cx="31"
                cy="1.66683"
                r="1.66667"
                transform="rotate(-90 31 1.66683)"
                fill="white"></circle>
              <circle
                cx="45.6667"
                cy="45.3336"
                r="1.66667"
                transform="rotate(-90 45.6667 45.3336)"
                fill="white"></circle>
              <circle
                cx="45.6667"
                cy="1.66683"
                r="1.66667"
                transform="rotate(-90 45.6667 1.66683)"
                fill="white"></circle>
              <circle
                cx="60.3333"
                cy="45.3338"
                r="1.66667"
                transform="rotate(-90 60.3333 45.3338)"
                fill="white"></circle>
              <circle
                cx="60.3333"
                cy="1.66707"
                r="1.66667"
                transform="rotate(-90 60.3333 1.66707)"
                fill="white"></circle>
              <circle
                cx="88.6667"
                cy="45.3338"
                r="1.66667"
                transform="rotate(-90 88.6667 45.3338)"
                fill="white"></circle>
              <circle
                cx="88.6667"
                cy="1.66707"
                r="1.66667"
                transform="rotate(-90 88.6667 1.66707)"
                fill="white"></circle>
              <circle
                cx="117.667"
                cy="45.3338"
                r="1.66667"
                transform="rotate(-90 117.667 45.3338)"
                fill="white"></circle>
              <circle
                cx="117.667"
                cy="1.66707"
                r="1.66667"
                transform="rotate(-90 117.667 1.66707)"
                fill="white"></circle>
              <circle
                cx="74.6667"
                cy="45.3338"
                r="1.66667"
                transform="rotate(-90 74.6667 45.3338)"
                fill="white"></circle>
              <circle
                cx="74.6667"
                cy="1.66707"
                r="1.66667"
                transform="rotate(-90 74.6667 1.66707)"
                fill="white"></circle>
              <circle
                cx="103"
                cy="45.3338"
                r="1.66667"
                transform="rotate(-90 103 45.3338)"
                fill="white"></circle>
              <circle
                cx="103"
                cy="1.66707"
                r="1.66667"
                transform="rotate(-90 103 1.66707)"
                fill="white"></circle>
              <circle
                cx="132"
                cy="45.3338"
                r="1.66667"
                transform="rotate(-90 132 45.3338)"
                fill="white"></circle>
              <circle
                cx="132"
                cy="1.66707"
                r="1.66667"
                transform="rotate(-90 132 1.66707)"
                fill="white"></circle>
            </svg>
          </div>
          <div class="absolute -right-6 -top-6 z-[-1]">
            <svg
              width="134"
              height="106"
              viewBox="0 0 134 106"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle
                cx="1.66667"
                cy="104"
                r="1.66667"
                transform="rotate(-90 1.66667 104)"
                fill="white"></circle>
              <circle
                cx="16.3333"
                cy="104"
                r="1.66667"
                transform="rotate(-90 16.3333 104)"
                fill="white"></circle>
              <circle cx="31" cy="104" r="1.66667" transform="rotate(-90 31 104)" fill="white"
              ></circle>
              <circle
                cx="45.6667"
                cy="104"
                r="1.66667"
                transform="rotate(-90 45.6667 104)"
                fill="white"></circle>
              <circle
                cx="60.3333"
                cy="104"
                r="1.66667"
                transform="rotate(-90 60.3333 104)"
                fill="white"></circle>
              <circle
                cx="88.6667"
                cy="104"
                r="1.66667"
                transform="rotate(-90 88.6667 104)"
                fill="white"></circle>
              <circle
                cx="117.667"
                cy="104"
                r="1.66667"
                transform="rotate(-90 117.667 104)"
                fill="white"></circle>
              <circle
                cx="74.6667"
                cy="104"
                r="1.66667"
                transform="rotate(-90 74.6667 104)"
                fill="white"></circle>
              <circle cx="103" cy="104" r="1.66667" transform="rotate(-90 103 104)" fill="white"
              ></circle>
              <circle cx="132" cy="104" r="1.66667" transform="rotate(-90 132 104)" fill="white"
              ></circle>
              <circle
                cx="1.66667"
                cy="89.3333"
                r="1.66667"
                transform="rotate(-90 1.66667 89.3333)"
                fill="white"></circle>
              <circle
                cx="16.3333"
                cy="89.3333"
                r="1.66667"
                transform="rotate(-90 16.3333 89.3333)"
                fill="white"></circle>
              <circle
                cx="31"
                cy="89.3333"
                r="1.66667"
                transform="rotate(-90 31 89.3333)"
                fill="white"></circle>
              <circle
                cx="45.6667"
                cy="89.3333"
                r="1.66667"
                transform="rotate(-90 45.6667 89.3333)"
                fill="white"></circle>
              <circle
                cx="60.3333"
                cy="89.3338"
                r="1.66667"
                transform="rotate(-90 60.3333 89.3338)"
                fill="white"></circle>
              <circle
                cx="88.6667"
                cy="89.3338"
                r="1.66667"
                transform="rotate(-90 88.6667 89.3338)"
                fill="white"></circle>
              <circle
                cx="117.667"
                cy="89.3338"
                r="1.66667"
                transform="rotate(-90 117.667 89.3338)"
                fill="white"></circle>
              <circle
                cx="74.6667"
                cy="89.3338"
                r="1.66667"
                transform="rotate(-90 74.6667 89.3338)"
                fill="white"></circle>
              <circle
                cx="103"
                cy="89.3338"
                r="1.66667"
                transform="rotate(-90 103 89.3338)"
                fill="white"></circle>
              <circle
                cx="132"
                cy="89.3338"
                r="1.66667"
                transform="rotate(-90 132 89.3338)"
                fill="white"></circle>
              <circle
                cx="1.66667"
                cy="74.6673"
                r="1.66667"
                transform="rotate(-90 1.66667 74.6673)"
                fill="white"></circle>
              <circle
                cx="1.66667"
                cy="31.0003"
                r="1.66667"
                transform="rotate(-90 1.66667 31.0003)"
                fill="white"></circle>
              <circle
                cx="16.3333"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 16.3333 74.6668)"
                fill="white"></circle>
              <circle
                cx="16.3333"
                cy="31.0003"
                r="1.66667"
                transform="rotate(-90 16.3333 31.0003)"
                fill="white"></circle>
              <circle
                cx="31"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 31 74.6668)"
                fill="white"></circle>
              <circle
                cx="31"
                cy="31.0003"
                r="1.66667"
                transform="rotate(-90 31 31.0003)"
                fill="white"></circle>
              <circle
                cx="45.6667"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 45.6667 74.6668)"
                fill="white"></circle>
              <circle
                cx="45.6667"
                cy="31.0003"
                r="1.66667"
                transform="rotate(-90 45.6667 31.0003)"
                fill="white"></circle>
              <circle
                cx="60.3333"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 60.3333 74.6668)"
                fill="white"></circle>
              <circle
                cx="60.3333"
                cy="31.0001"
                r="1.66667"
                transform="rotate(-90 60.3333 31.0001)"
                fill="white"></circle>
              <circle
                cx="88.6667"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 88.6667 74.6668)"
                fill="white"></circle>
              <circle
                cx="88.6667"
                cy="31.0001"
                r="1.66667"
                transform="rotate(-90 88.6667 31.0001)"
                fill="white"></circle>
              <circle
                cx="117.667"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 117.667 74.6668)"
                fill="white"></circle>
              <circle
                cx="117.667"
                cy="31.0001"
                r="1.66667"
                transform="rotate(-90 117.667 31.0001)"
                fill="white"></circle>
              <circle
                cx="74.6667"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 74.6667 74.6668)"
                fill="white"></circle>
              <circle
                cx="74.6667"
                cy="31.0001"
                r="1.66667"
                transform="rotate(-90 74.6667 31.0001)"
                fill="white"></circle>
              <circle
                cx="103"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 103 74.6668)"
                fill="white"></circle>
              <circle
                cx="103"
                cy="31.0001"
                r="1.66667"
                transform="rotate(-90 103 31.0001)"
                fill="white"></circle>
              <circle
                cx="132"
                cy="74.6668"
                r="1.66667"
                transform="rotate(-90 132 74.6668)"
                fill="white"></circle>
              <circle
                cx="132"
                cy="31.0001"
                r="1.66667"
                transform="rotate(-90 132 31.0001)"
                fill="white"></circle>
              <circle
                cx="1.66667"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 1.66667 60.0003)"
                fill="white"></circle>
              <circle
                cx="1.66667"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 1.66667 16.3336)"
                fill="white"></circle>
              <circle
                cx="16.3333"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 16.3333 60.0003)"
                fill="white"></circle>
              <circle
                cx="16.3333"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 16.3333 16.3336)"
                fill="white"></circle>
              <circle
                cx="31"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 31 60.0003)"
                fill="white"></circle>
              <circle
                cx="31"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 31 16.3336)"
                fill="white"></circle>
              <circle
                cx="45.6667"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 45.6667 60.0003)"
                fill="white"></circle>
              <circle
                cx="45.6667"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 45.6667 16.3336)"
                fill="white"></circle>
              <circle
                cx="60.3333"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 60.3333 60.0003)"
                fill="white"></circle>
              <circle
                cx="60.3333"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 60.3333 16.3336)"
                fill="white"></circle>
              <circle
                cx="88.6667"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 88.6667 60.0003)"
                fill="white"></circle>
              <circle
                cx="88.6667"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 88.6667 16.3336)"
                fill="white"></circle>
              <circle
                cx="117.667"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 117.667 60.0003)"
                fill="white"></circle>
              <circle
                cx="117.667"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 117.667 16.3336)"
                fill="white"></circle>
              <circle
                cx="74.6667"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 74.6667 60.0003)"
                fill="white"></circle>
              <circle
                cx="74.6667"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 74.6667 16.3336)"
                fill="white"></circle>
              <circle
                cx="103"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 103 60.0003)"
                fill="white"></circle>
              <circle
                cx="103"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 103 16.3336)"
                fill="white"></circle>
              <circle
                cx="132"
                cy="60.0003"
                r="1.66667"
                transform="rotate(-90 132 60.0003)"
                fill="white"></circle>
              <circle
                cx="132"
                cy="16.3336"
                r="1.66667"
                transform="rotate(-90 132 16.3336)"
                fill="white"></circle>
              <circle
                cx="1.66667"
                cy="45.3336"
                r="1.66667"
                transform="rotate(-90 1.66667 45.3336)"
                fill="white"></circle>
              <circle
                cx="1.66667"
                cy="1.66683"
                r="1.66667"
                transform="rotate(-90 1.66667 1.66683)"
                fill="white"></circle>
              <circle
                cx="16.3333"
                cy="45.3336"
                r="1.66667"
                transform="rotate(-90 16.3333 45.3336)"
                fill="white"></circle>
              <circle
                cx="16.3333"
                cy="1.66683"
                r="1.66667"
                transform="rotate(-90 16.3333 1.66683)"
                fill="white"></circle>
              <circle
                cx="31"
                cy="45.3336"
                r="1.66667"
                transform="rotate(-90 31 45.3336)"
                fill="white"></circle>
              <circle
                cx="31"
                cy="1.66683"
                r="1.66667"
                transform="rotate(-90 31 1.66683)"
                fill="white"></circle>
              <circle
                cx="45.6667"
                cy="45.3336"
                r="1.66667"
                transform="rotate(-90 45.6667 45.3336)"
                fill="white"></circle>
              <circle
                cx="45.6667"
                cy="1.66683"
                r="1.66667"
                transform="rotate(-90 45.6667 1.66683)"
                fill="white"></circle>
              <circle
                cx="60.3333"
                cy="45.3338"
                r="1.66667"
                transform="rotate(-90 60.3333 45.3338)"
                fill="white"></circle>
              <circle
                cx="60.3333"
                cy="1.66707"
                r="1.66667"
                transform="rotate(-90 60.3333 1.66707)"
                fill="white"></circle>
              <circle
                cx="88.6667"
                cy="45.3338"
                r="1.66667"
                transform="rotate(-90 88.6667 45.3338)"
                fill="white"></circle>
              <circle
                cx="88.6667"
                cy="1.66707"
                r="1.66667"
                transform="rotate(-90 88.6667 1.66707)"
                fill="white"></circle>
              <circle
                cx="117.667"
                cy="45.3338"
                r="1.66667"
                transform="rotate(-90 117.667 45.3338)"
                fill="white"></circle>
              <circle
                cx="117.667"
                cy="1.66707"
                r="1.66667"
                transform="rotate(-90 117.667 1.66707)"
                fill="white"></circle>
              <circle
                cx="74.6667"
                cy="45.3338"
                r="1.66667"
                transform="rotate(-90 74.6667 45.3338)"
                fill="white"></circle>
              <circle
                cx="74.6667"
                cy="1.66707"
                r="1.66667"
                transform="rotate(-90 74.6667 1.66707)"
                fill="white"></circle>
              <circle
                cx="103"
                cy="45.3338"
                r="1.66667"
                transform="rotate(-90 103 45.3338)"
                fill="white"></circle>
              <circle
                cx="103"
                cy="1.66707"
                r="1.66667"
                transform="rotate(-90 103 1.66707)"
                fill="white"></circle>
              <circle
                cx="132"
                cy="45.3338"
                r="1.66667"
                transform="rotate(-90 132 45.3338)"
                fill="white"></circle>
              <circle
                cx="132"
                cy="1.66707"
                r="1.66667"
                transform="rotate(-90 132 1.66707)"
                fill="white"></circle>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- ====== Hero Section End -->
